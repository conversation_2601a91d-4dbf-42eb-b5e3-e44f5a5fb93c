import {Injectable} from '@angular/core';
import {LicenseConfigurations, StepConfiguration, StepPage, StepParams, StepperConfiguration} from '../models/deduplication.models';
import {RelationshipTypesListComponent} from '../components/wizard/steps/relationship-types-list.component';
import {RelationshipWizardStep2Component} from '../components/wizard/steps/relationship-wizard-md-selection.component';
import {RelationshipMaterialEnrich} from '../components/wizard/steps/relationship-material-enrich.component';
import {RelationshipPlantDataEnrich} from '../components/wizard/steps/relationship-plant-data-enrich.component';
import {RelationshipMaterialStatusComponent} from '../components/wizard/steps/relationship-material-status.component';
import {DeduplicationSummaryComponent} from '../components/wizard/steps/deduplication-summary.component';
import { RelationshipType } from '../../relationships/endpoint/relationships.model';

type StepCreationMethod = () => StepConfiguration;

@Injectable({
  providedIn: 'root'
})
export class DeduplicationStepFactoryService {

  private componentMap: { [key: string]: any } = {};
  private methodMap: { [key: string]: StepCreationMethod } = {};

  constructor() {
    this.initializeComponentMap();
    this.initializeMethodMap();
  }

  private initializeComponentMap(): void {
    this.componentMap['relationship-types-list'] = RelationshipTypesListComponent;
    this.componentMap['relationship-wizard-md-selection'] = RelationshipWizardStep2Component;
    this.componentMap['relationship-material-status'] = RelationshipMaterialStatusComponent;
    this.componentMap['relationship-material-enrich'] = RelationshipMaterialEnrich;
    this.componentMap['relationship-plant-data-enrich'] = RelationshipPlantDataEnrich;
    this.componentMap['deduplication-summary'] = DeduplicationSummaryComponent;
  }

  private initializeMethodMap(): void {
    this.methodMap[StepPage.RELATIONSHIP_TYPE.toString()] = () => this.createRelationshipTypesStep();
    this.methodMap[StepPage.MATERIALS_SELECTION.toString()] = () => this.createMaterialSelectionStep();
    this.methodMap[StepPage.MATERIALS_STATUS.toString()] = () => this.createMaterialStatusStep();
    this.methodMap[StepPage.MATERIAL_ENRICHMENT.toString()] = () => this.createMaterialEnrichmentStep();
    this.methodMap[StepPage.PLANT_DATA_ENRICHMENT.toString()] = () => this.createPlantDataEnrichmentStep();
    this.methodMap[StepPage.SUMMARY.toString()] = () => this.createSummaryStep();
  }

  getStepComponent(stepType: string): any {
    if (this.componentMap[stepType]) {
      return this.componentMap[stepType];
    }
    console.warn(`Step component not found for type: ${stepType}`);
    return null;
  }

  createStepperConfiguration(stepParams: StepParams): StepperConfiguration {
    const steps: StepConfiguration[] = [];

    steps.push(this.createRelationshipTypesStep());
    steps.push(this.createMaterialSelectionStep());

    if (!stepParams.isCreatingGoldenRecord && stepParams.relationshipType === RelationshipType.DUPLICATE) {
      if (stepParams.licenseConfigurations?.deduplicationMasterDataStatusEnabled) {
        steps.push(this.createMaterialStatusStep());
      }
      if (stepParams.licenseConfigurations?.deduplicationBasicDataEnrichment) {
        const currentStep = this.createMaterialEnrichmentStep({
          clientsConfiguration: stepParams.setup
        });
        const clientKeys = Object.keys(stepParams.setup || {});
        if (clientKeys.length > 0) {
          clientKeys.forEach(client => {
            if (stepParams.setup[client].secondaries?.length > 0) {
              currentStep.subSteps.push(this.createSubStep(currentStep, {
                client,
                secondaries: stepParams.setup[client].secondaries || [],
              }));
            }
          });
        }
        if (currentStep.subSteps?.length > 0) { steps.push(currentStep); }
      }

      if (stepParams.licenseConfigurations?.deduplicationPlantEnrichment) {
        const currentStep = this.createPlantDataEnrichmentStep({ clientsConfiguration: stepParams.setup });
        Object.keys(stepParams.setup || {}).forEach(client => {
          const configuration = stepParams.setup[client];
          if (configuration?.plantExtensionInformations?.length > 0) {
            configuration.plantExtensionInformations?.forEach(plantInformation => {
              currentStep.subSteps.push(
                  this.createSubStep(currentStep, {
                    client,
                    secondaries: configuration.secondaries || [],
                    plantInformation
                  })
              );
            });
          }
        });
        if (currentStep.subSteps?.length > 0) { steps.push(currentStep); }
      }
    }

    steps.push(this.createSummaryStep());

    return {
      steps
    };
  }

  private createRelationshipTypesStep(): StepConfiguration {
    return {
      stepType: 'relationship-types-list',
      stepPage: StepPage.RELATIONSHIP_TYPE,
      headerKey: 'layout.relationship-popup.steps.step1',
      icon: 'fas fa-chart-network',
      component: this.componentMap['relationship-types-list'],
      isVisible: (stepParams: StepParams) => true,
      isEnabled: (stepParams: StepParams) => true,
      inputs: {},
      position: 'first'
    };
  }

  private createMaterialSelectionStep(): StepConfiguration {
    return {
      stepType: 'relationship-wizard-md-selection',
      stepPage: StepPage.MATERIALS_SELECTION,
      headerKey: 'layout.relationship-popup.steps.step2-' + this.adaptMdDomain("M"),
      icon: 'fas fa-file-spreadsheet',
      component: this.componentMap['relationship-wizard-md-selection'],
      isVisible: (stepParams: StepParams) => true,
      isEnabled: (stepParams: StepParams) => true,
      inputs: {},
      position: 'middle'
    };
  }

  private createMaterialStatusStep(): StepConfiguration {
    return {
      stepType: 'relationship-material-status',
      stepPage: StepPage.MATERIALS_STATUS,
      headerKey: 'deduplication.steps.materialStatus',
      icon: 'fas fa-tasks',
      component: this.componentMap['relationship-material-status'],
      isVisible: (stepParams: StepParams) => {
        return !stepParams.isCreatingGoldenRecord && stepParams.licenseConfigurations?.deduplicationMasterDataStatusEnabled === true;
      },
      isEnabled: (stepParams: StepParams) => {
        return !stepParams.isCreatingGoldenRecord && stepParams.licenseConfigurations?.deduplicationMasterDataStatusEnabled === true;
      },
      inputs: {},
      position: 'middle'
    };
  }

  private createMaterialEnrichmentStep(inputs?: Record<string, any>): StepConfiguration {
    return {
      stepType: 'relationship-material-enrich',
      stepPage: StepPage.MATERIAL_ENRICHMENT,
      headerKey: 'deduplication.steps.materialEnrich',
      icon: 'fas fa-check-circle',
      component: this.componentMap['relationship-material-enrich'],
      isVisible: (stepParams: StepParams) => {
        return !stepParams.isCreatingGoldenRecord && stepParams.licenseConfigurations?.deduplicationBasicDataEnrichment === true;
      },
      isEnabled: (stepParams: StepParams) => {
        return !stepParams.isCreatingGoldenRecord && stepParams.licenseConfigurations?.deduplicationBasicDataEnrichment === true;
      },
      inputs,
      position: 'middle',
      subSteps: []
    };
  }

  private createPlantDataEnrichmentStep(inputs?: Record<string, any>): StepConfiguration {
    return {
      stepType: 'relationship-plant-data-enrich',
      stepPage: StepPage.PLANT_DATA_ENRICHMENT,
      headerKey: 'deduplication.steps.plantEnrich',
      icon: 'fas fa-thumbs-up',
      component: this.componentMap['relationship-plant-data-enrich'],
      isVisible: (stepParams: StepParams) => {
        return !stepParams.isCreatingGoldenRecord && stepParams.licenseConfigurations?.deduplicationPlantEnrichment === true;
      },
      isEnabled: (stepParams: StepParams) => {
        return !stepParams.isCreatingGoldenRecord && stepParams.licenseConfigurations?.deduplicationPlantEnrichment === true;
      },
      inputs,
      position: 'middle',
      subSteps: []
    };
  }

  private createSubStep(parentStep: StepConfiguration, inputs: Record<string, any>): StepConfiguration {
    return {
      stepType: 'relationship-substep',
      stepPage: parentStep.stepPage,
      headerKey: 'deduplication.steps.materialEnrich',
      icon: 'fas fa-check-circle',
      component: null,
      isVisible: (stepParams: StepParams) => false,
      isEnabled: (stepParams: StepParams) => true,
      inputs,
      position: 'substep'
    };
  }

  private createSummaryStep(): StepConfiguration {
    return {
      stepType: 'deduplication-summary',
      stepPage: StepPage.SUMMARY,
      headerKey: 'deduplication.steps.summary.header',
      icon: 'fas fa-file-check',
      component: this.componentMap['deduplication-summary'],
      isVisible: (stepParams: StepParams) => true,
      isEnabled: (stepParams: StepParams) => true,
      inputs: {},
      position: 'last'
    };
  }

  private adaptMdDomain(mdDomain: string) {
    switch (mdDomain) {
      case 'M':
        return "materials";
      case 'S':
        return "services";
      default:
        return "materials";
    }
  }

  getVisibleSteps(stepParams: StepParams): StepConfiguration[] {
    const stepperConfig = this.createStepperConfiguration(stepParams);
    return stepperConfig.steps.filter(step =>
      step.isEnabled(stepParams)
    );
  }

  getEnabledSteps(stepParams: StepParams): StepConfiguration[] {
    const visibleSteps = this.getVisibleSteps(stepParams);
    const steps = visibleSteps.filter(step =>
        step.isEnabled(stepParams)
    );
    return [...steps];
  }


  getStepByIndex(index: number, stepParams: StepParams): StepConfiguration | null {
    const visibleSteps = this.getVisibleSteps(stepParams);
    return visibleSteps[index] || null;
  }

  getStepIndexByType(stepType: string, stepParams: StepParams): number {
    const visibleSteps = this.getVisibleSteps(stepParams);
    return visibleSteps.findIndex(step => step.stepType === stepType);
  }

  getTotalStepsCount(stepParams: StepParams): number {
    return this.getVisibleSteps(stepParams).length;
  }

  getStepByStepPage(stepParams: StepParams): StepConfiguration | null {
    const visibleSteps = this.getVisibleSteps(stepParams);
    return visibleSteps.find(step => step.stepPage === stepParams.stepPage) || null;
  }

  getStepIndexByStepPage(stepParams: StepParams): number {
    const visibleSteps = this.getVisibleSteps(stepParams);
    return visibleSteps.findIndex(step => step.stepPage === stepParams.stepPage);
  }

  isStepVisible(stepParams: StepParams): boolean {
    const stepMethod = this.methodMap[stepParams.stepPage?.toString()];
    if (!stepMethod) { return false; }

    const step = stepMethod();
    return step.isVisible(stepParams);
  }

  isStepEnabled(stepParams: StepParams): boolean {
    const stepMethod = this.methodMap[stepParams.stepPage?.toString()];
    if (!stepMethod) { return false; }

    const step = stepMethod();
    return step.isEnabled(stepParams);
  }

  isPreviousStepEnabled(stepParams: StepParams): boolean {
    return stepParams.stepPage > 0;
  }

  isNextStepEnabled(stepParams: StepParams): boolean {
    const visibleSteps = this.getVisibleSteps(stepParams);
    const currentIndex = visibleSteps.findIndex(step => step.stepPage === stepParams.stepPage);
    const nextStep = visibleSteps[currentIndex + 1];
    if (!nextStep) {
      return false;
    }
    return nextStep.isEnabled(stepParams);
  }

  isLastStep(stepParams: StepParams, stepper: StepConfiguration[]): boolean {
    console.log(stepParams.stepPage, stepper, stepper[stepParams.stepPage].stepPage);
    // return stepper && stepParams.stepPage === stepper[stepper?.length - 1]?.stepPage;
    return stepper[stepParams?.stepPage]?.stepPage === StepPage.SUMMARY;
  }

  getEnabledStepsByLicense(stepParams: StepParams): StepConfiguration[] {
    const visibleSteps = this.getVisibleSteps(stepParams);
    const steps = visibleSteps.filter(step =>
        step.isEnabled(stepParams)
    );
    return [...steps];
  }

  isPlantStep(stepParams: StepParams): boolean {
    return stepParams.stepPage === StepPage.PLANT_DATA_ENRICHMENT;
  }

  mapLicenseToConfiguration(licenseParameters: {[key: string]: any}): LicenseConfigurations {
    return  {
        deduplicationBasicDataEnrichment: licenseParameters?.deduplicationBasicDataEnrichment ?? false,
        deduplicationPlantEnrichment: licenseParameters?.deduplicationPlantEnrichment ?? false,
        deduplicationSubTypesEnabled: licenseParameters?.deduplicationSubTypesEnabled ?? false,
        deduplicationMasterDataStatusEnabled: licenseParameters.deduplicationMasterDataStatusEnabled ?? false,
        deduplicationMasterDataStatusRequired: licenseParameters.deduplicationMasterDataStatusRequired ?? false
    };
  }

}
