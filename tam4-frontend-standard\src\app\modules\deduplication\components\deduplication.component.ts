import {CommonModule} from '@angular/common';
import {Component, computed, inject, OnInit} from '@angular/core';
import {Router} from '@angular/router';
import {SelectorMap} from '@creactives/models';
import {Tam4TranslationService} from '@creactives/tam4-translation-core';
import {Store} from '@ngrx/store';
import {TranslateModule, TranslateService} from '@ngx-translate/core';
import {Tam4SelectorConfig, TamAbstractReduxComponent} from 'src/app/components';
import {MDDomain} from 'src/app/modules/components/types';
import {getCurrentValues, getLicenceParameters } from 'src/app/modules/layout/store/reducers';
import {DeduplicationService} from '../services/deduplication.service';
import {DeduplicationSelectors} from '../store/deduplication.selectors';

import {Button} from 'primeng/button';
import {DeduplicationStepFactoryService} from '../services/deduplication-step-factory.service';
import {NavigationStepsComponent} from './navigation-steps/navigation-steps.component';
import {WizardComponent} from './wizard/wizard.component';
import {ComponentsModule} from '../../components/components.module';
import {TamFullHeightPanelComponent} from '../../../components/tam-full-height-panel.component';
import {PrimeTemplate} from 'primeng/api';
import {DefaultScrollPanelProperties} from '../../../utils/common.constants';
import {PanelModule} from 'primeng/panel';
import { DEDUPLICATION_ACTIONS } from '../store/deduplication.actions';
import { ViewModeEnum } from '../../materials-editor/models/material-editor.types';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'loading', selector: DeduplicationSelectors.getIsLoading},
  {key: 'hasError', selector: DeduplicationSelectors.getHasError},
  {key: 'subtypeRelationship', selector: DeduplicationSelectors.getSubtypesRelationships},
  {key: 'deduplicationRequest', selector: DeduplicationSelectors.getDeduplicationRequest},
  {key: 'step', selector: DeduplicationSelectors.getStep},
  {key: 'stepper', selector: DeduplicationSelectors.getStepper},
  {key: 'stepNumber', selector: DeduplicationSelectors.getStepNumber},
  {key: 'licenseConfigurations', selector: DeduplicationSelectors.getLicenseConfiguration},
  {key: 'isCreatingGoldenRecord', selector: DeduplicationSelectors.getIsCreatingGoldenRecord}
];

@Component({
  selector: 'create-deduplication',
  template: `
    <tam-progress-spinner [active]="signals?.loading()"></tam-progress-spinner>
    <div tamFullHeightPanel>
      <!-- <layout-error-view *ngIf="!signals?.loading() && signals?.hasError()"
                        [errorMessage]="errorMessage$ | async"></layout-error-view> -->
      <div class="flex-1 nice-scroll" [style]="DefaultScrollPanelProperties">
          <div class="mt-3" #categorySelectionSel>
            <form class="form-group">
              @if (signals?.step()) {
                <deduplication-navigation-steps
                  [currentStep]="signals.step().stepPage"
                  [mdDomain]="mdDomain"
                  [steps]="signals?.stepper()"
                ></deduplication-navigation-steps>
                <p-panel class="compat mt-3">
                <relationship-management-wizard
                    [step]="signals.step()"
                    [stepNumber]="signals.step().stepPage"
                    [mdDomain]="mdDomain"
                    [subtypeRelationship] ="signals.subtypeRelationship"
                    [deduplicationRequest] ="signals.deduplicationRequest"
                ></relationship-management-wizard>
                </p-panel>
              }
            </form>
          </div>
      </div>
        <ng-template pTemplate="footer">
          <div class="flex-0">
            @if(previousStepEnabled) {
              <p-button class="mr-1" label="{{'deduplication.steps.button.cancel' | translate}}"
                        icon="fas fa-delete-left"
                        (onClick)="cancelOperation()"/>
              <p-button
                  label="{{'deduplication.steps.button.back' | translate}}"
                  icon="pi pi-arrow-left"
                  (onClick)="goToPreviousStep()" />
            }
          </div>
          <div class="flex-0">
            @if(isPlantStep) {
              <p-button
                  label="{{ 'deduplication.enrichment.skipPlant' | translate }}"
                  icon="pi pi-arrow-right"
                  styleClass="p-button-secondary mr-1"
                  (onClick)="onSkipPlant()">
              </p-button>
            }
            @if(nextStepEnabled) {
              <p-button
                  label="{{'deduplication.steps.button.next' | translate}}"
                  icon="pi pi-arrow-right"
                  iconPos="right"
                  (onClick)="goToNextStep()" />
            }
            @if(isLastStep) {
              <p-button
                label="{{'deduplication.steps.button.confirm' | translate}}"
                icon="pi pi-arrow-right"
                iconPos="right"
                (onClick)="confirm()" />
              }
          </div>
        </ng-template>
    </div>
  `,
  standalone: true,
  imports: [
    CommonModule,
    NavigationStepsComponent,
    WizardComponent,
    Button,
    TranslateModule,
    ComponentsModule,
    TamFullHeightPanelComponent,
    PrimeTemplate,
    PanelModule
  ]
})
export class DeduplicationComponent extends TamAbstractReduxComponent<SelectorMap> implements OnInit{

  constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store){
    super(translate, tamTranslate, store, storeSelectors);
  }

  mdDomain: MDDomain;

  subtypeRelationship = computed(() => this.signals?.subtypeRelationship());
  router = inject(Router);
  service = inject(DeduplicationService);
  deduplicationStepFactoryService = inject(DeduplicationStepFactoryService);

  protected readonly DefaultScrollPanelProperties = DefaultScrollPanelProperties;

  ngOnInit() {
    const materialIds = history.state?.materialIds ?? [];
    if (!(materialIds?.length > 0) && history.state?.viewMode !== ViewModeEnum.ADDITIONAL_INFO) {
      this.router.navigate(['app', 'duplicates', 'groups-all']);
    }

    if (history.state?.viewMode !== ViewModeEnum.ADDITIONAL_INFO) {
      this.service.action_doSetMaterialIds(materialIds);
    } else {
      this.service.action_doGetRelationshipMaterialsDetails();
    }

    this.service.action_doInitStepper();

    this.store.select(getCurrentValues).subscribe(globalFilterValues => {
      this.mdDomain = globalFilterValues.mdDomain;
    });
    this.store.select(getLicenceParameters).subscribe(licenseParameters => {
      this.service.initLicenseConfiguration(licenseParameters);
      if (licenseParameters?.deduplicationSubTypesEnabled) {
        this.service.action_doSubtypesRelationship();
      }
    });
  }


  confirm() {
    this.store.dispatch(DEDUPLICATION_ACTIONS.OPEN_CONFIRM_DIALOG());
  }

  goToPreviousStep() {
    const currentStepNumber = this.signals?.stepNumber() || 0;
    if (currentStepNumber > 0) {
      this.service.action_doGoToPreviousStep();
    }
  }

  cancelOperation(){
    const currentStepNumber = this.signals?.stepNumber() || 0;
    if (currentStepNumber > 0) {
      this.service.action_cancelOperation();
    }
  }

  goToNextStep() {
    // const currentStepNumber = this.signals?.stepNumber() || 0;
    // const licenseConfig = this.signals?.licenseConfigurations();
    // const isCreatingGoldenRecord = this.signals?.isCreatingGoldenRecord() || false;
    //
    // if (!licenseConfig) {
    //   return;
    // }
    //
    // const visibleSteps = this.deduplicationStepFactoryService.getVisibleSteps({
    //   licenseConfigurations: licenseConfig,
    //   isCreatingGoldenRecord,
    //   relationshipType: this.signals?.deduplicationRequest().relationshipType
    // });
    // const isCurrentStepValid = this.signals?.step()?.isValid || false;
    //
    // if (/*isCurrentStepValid &&*/ currentStepNumber < (visibleSteps.length - 1)) {
    //     this.service.action_doGoToNextStep();
    // // }
    // }
    this.service.action_doGoToNextStep();
  }

  get previousStepEnabled(): boolean {
    return this.deduplicationStepFactoryService.isPreviousStepEnabled(this.signals?.step());
  }

  get nextStepEnabled(): boolean {
    return this.deduplicationStepFactoryService.isNextStepEnabled(this.signals?.step());
  }

  get isLastStep(): boolean {
    return this.deduplicationStepFactoryService.isLastStep(this.signals?.step());
  }

  get isPlantStep(): boolean {
    return this.deduplicationStepFactoryService.isPlantStep(this.signals?.step());
  }

  onSkipPlant() {
    this.service.action_doSkipPlant();
  }

  ngOnDestroy() {
    this.service.action_doInit();
    super.ngOnDestroy();
  }

}
