import {
    CreateDeduplicationRequestBE,
    DeduplicationBasicDataRowDto,
    DeduplicationFieldConfiguration, DeduplicationSetup,
    DeduplicationSetupInformation,
    DeduplicationStep,
    RelationshipMaterialInfo
} from '../models/deduplication.models';
import {RelationshipTypes} from '../../layout/store/actions/popup.actions';
import {createSelector} from '@ngrx/store';
import {DeduplicationSelectors} from '../store/deduplication.selectors';
import {DeduplicationState} from '../store/deduplication.state';

export function getRole(
    it: RelationshipMaterialInfo,
    type: string
) {
    const relationshipType = relationshipTypeFromString(type.toLowerCase()); // TODO: Check this!!!
    switch (relationshipType) {
        case RelationshipTypes.equivalence:
            return 'EQUIVALENT';
        case RelationshipTypes.interchangeable:
            return it.primary ? 'CAN_SUBSTITUTE' : 'CAN_BE_SUBSTITUTED_BY';
        case RelationshipTypes.duplicate:
            return it.primary ? 'PRIMARY' : 'SECONDARY';
        case RelationshipTypes.noRelationship:
            return 'NO_RELATIONSHIP';
    }
    throw new Error();
}

// TODO: Simplify this function
export function relationshipTypeFromString(type: string): RelationshipTypes {
    switch (type) {
        case 'equivalence':
            return RelationshipTypes.equivalence;
        case 'interchangeable':
            return RelationshipTypes.interchangeable;
        case 'duplicate':
            return RelationshipTypes.duplicate;
        case 'norelationship':
            return RelationshipTypes.noRelationship;
        default:
            throw new Error(`Unsupported relationship type: ${type}`);
    }
}

export function applyMaterialSecondarySelection(
    row: DeduplicationBasicDataRowDto,
    selectedSecondary: DeduplicationFieldConfiguration | undefined,
    selectedMaterialKey: string,
    toggle: boolean,
    enrichedData: { enrichmentStatus: "NOT_CHANGED" | "EDITED"; fields: Record<string, DeduplicationFieldConfiguration> }
): void {
    if (!selectedSecondary?.editable || !selectedSecondary.value || selectedSecondary.value === '') {
        return;
    }

    if (toggle) {
        selectedSecondary.selected = selectedSecondary.selected === undefined ? true : !selectedSecondary.selected;
    }

    const primaryKeys = Object.keys(row.primary || {});
    const primaryKey = primaryKeys.length > 0 ? primaryKeys[0] : null;

    const enrichedPrimaryField = enrichedData.fields[row.id];
    if (!enrichedPrimaryField) {
        console.warn(`Enriched data not found for primaryKey=${primaryKey}`);
        return;
    }

    if (selectedSecondary.selected) {
        Object.entries(row.secondaries).forEach(([materialKey, secondary]) => {
            if (
                materialKey !== selectedMaterialKey &&
                secondary &&
                secondary.editable &&
                secondary.selected &&
                secondary.id === selectedSecondary.id
            ) {
                secondary.selected = false;
            }
        });

        if (enrichedPrimaryField.oldValue === undefined) {
            enrichedPrimaryField.oldValue = enrichedPrimaryField.value;
        }
        enrichedPrimaryField.value = `${selectedSecondary.value}`;
        // primaryField.value = selectedSecondary.value;
        // primaryField.edited = enrichedPrimaryField.oldValue !== enrichedPrimaryField.value;
    } else {
        if (enrichedPrimaryField.oldValue !== undefined) {
            enrichedPrimaryField.value = enrichedPrimaryField.oldValue;
            // primaryField.value = enrichedPrimaryField.oldValue;
            // primaryField.edited = enrichedPrimaryField.oldValue !== enrichedPrimaryField.value;
            delete enrichedPrimaryField.oldValue;
        } else {
            enrichedPrimaryField.value = '';
            // primaryField.value = '';
            // primaryField.edited = false;
        }
    }
    enrichedData.enrichmentStatus = "EDITED";
}

export function applyPlantSecondarySelection(
    row: DeduplicationBasicDataRowDto,
    selectedSecondary: DeduplicationFieldConfiguration | undefined,
    selectedMaterialKey: string,
    toggle: boolean,
    enrichedData: { enrichmentStatus: "NOT_CHANGED" | "EDITED" | "SKIPPED"; fields: Record<string, DeduplicationFieldConfiguration> }
): void {
    if (!selectedSecondary?.editable || !selectedSecondary.value || selectedSecondary.value === '') {
        return;
    }

    if (toggle) {
        selectedSecondary.selected = selectedSecondary.selected === undefined ? true : !selectedSecondary.selected;
    }

    const primaryKeys = Object.keys(row.primary || {});
    const primaryKey = primaryKeys.length > 0 ? primaryKeys[0] : null;
    const primaryField = primaryKeys.length > 0 ? row.primary[primaryKeys[0]] : null;

    if (!primaryField || !primaryKey) {
        console.warn(`Primary field or primary key not found for material: ${selectedMaterialKey}`);
        return;
    }

    const enrichedPrimaryField = enrichedData.fields[row.id];
    if (!enrichedPrimaryField) {
        console.warn(`Enriched data not found for primaryKey=${primaryKey}`);
        return;
    }

    if (selectedSecondary.selected) {
        Object.entries(row.secondaries).forEach(([materialKey, secondary]) => {
            if (
                materialKey !== selectedMaterialKey &&
                secondary &&
                secondary.editable &&
                secondary.selected &&
                secondary.id === selectedSecondary.id
            ) {
                secondary.selected = false;
            }
        });

        if (enrichedPrimaryField.oldValue === undefined) {
            enrichedPrimaryField.oldValue = enrichedPrimaryField.value;
        }
        enrichedPrimaryField.value = `${selectedSecondary.value}`;
        // primaryField.value = selectedSecondary.value;
        // primaryField.edited = enrichedPrimaryField.oldValue !== enrichedPrimaryField.value;
    } else {
        if (enrichedPrimaryField.oldValue !== undefined) {
            enrichedPrimaryField.value = enrichedPrimaryField.oldValue;
            // primaryField.value = enrichedPrimaryField.oldValue;
            // primaryField.edited = enrichedPrimaryField.oldValue !== enrichedPrimaryField.value;
            delete enrichedPrimaryField.oldValue;
        } else {
            enrichedPrimaryField.value = '';
            // primaryField.value = '';
            // primaryField.edited = false;
        }

    }
    enrichedData.enrichmentStatus = "EDITED";
}

export const getDataToSubmit =
    createSelector(DeduplicationSelectors.getFullFeatureState,
        (state) => {
            const payload: CreateDeduplicationRequestBE = {
                materialsInRelationship: state.groupMaterials?.materials
                    ?.filter((it) => it.selected)
                    ?.map((it) => ({
                        materialId: it.materialId,
                        role: getRole(it, state.deduplicationRequest.relationshipType),
                        materialStatus: it.status,
                        materialCode: it.materialCode,
                        client: it.client,
                        primary: it.primary,
                        selected: it.selected
                    })) || [],
                note: state.note,
                parentProcessId: '',
                relationshipType: state.deduplicationRequest?.relationshipType,
                creatingGoldenRecord: state.deduplicationRequest.isCreatingGoldenRecord,
            };
            return payload;
        }
    );

export function getActiveClientFromStepPage(step: DeduplicationStep): string {
    if (step.stepConfiguration?.subSteps?.length) {
        return step.stepConfiguration.subSteps[step.subStepPage ?? 0]?.inputs?.client ?? '';
    }

    const clientsConfig = step.stepConfiguration?.inputs?.clientsConfiguration as DeduplicationSetup | undefined;
    if (clientsConfig && typeof clientsConfig === 'object') {
        const firstClientKey = Object.keys(clientsConfig)[0];
        return firstClientKey ?? '';
    }
    return '';
}

export function getPlantInformationFromStepPage(
    step: DeduplicationStep
): DeduplicationSetupInformation | null {
    if (step.stepConfiguration?.subSteps?.length) {
        return step.stepConfiguration.subSteps[step.subStepPage ?? 0]?.inputs?.plantInformation ?? null;
    }
    return null;
}

export function updateSelectedDetailsOnState(newState: DeduplicationState) {
    const selectedMaterials = newState.groupMaterials.materials.filter(el => el.selected);
    newState.deduplicationRequest.materialsDeduplication = selectedMaterials;
    newState.groupClients = [...new Set(selectedMaterials.map(el => el.client))];
}

export function getDeduplicationInputsFromStepPage(
    step: DeduplicationStep
) {
    if (step.stepConfiguration?.subSteps?.length) {
        return step.stepConfiguration.subSteps[step.subStepPage ?? 0]?.inputs ?? null;
    }
    return null;
}
