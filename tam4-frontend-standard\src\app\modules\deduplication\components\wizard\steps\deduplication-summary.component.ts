import { CommonModule } from '@angular/common';
import { Component, Input, computed } from '@angular/core';
import { ReactiveFormsModule } from '@angular/forms';
import { SelectorMap } from '@creactives/models';
import { Tam4TranslationService, Tam4TranslationModule } from '@creactives/tam4-translation-core';
import { FontAwesomeModule } from '@fortawesome/angular-fontawesome';
import { Store } from '@ngrx/store';
import { TranslateModule, TranslateService } from '@ngx-translate/core';
import { TableModule } from 'primeng/table';
import { Tam4SelectorConfig, TamAbstractReduxComponent } from 'src/app/components';
import { SortByPrimaryPipe } from '../../../deduplication.pipe';
import { Subtype } from '../../../models/deduplication.models';
import { DeduplicationSelectors } from '../../../store/deduplication.selectors';

const storeSelectors: Tam4SelectorConfig[] = [
  {key: 'deduplicationRequest', selector: DeduplicationSelectors.getDeduplicationRequest},
  {key: 'enrichedPlantData', selector: DeduplicationSelectors.getEnrichedPlantData}
];

@Component({
  selector: 'deduplication-summary',
  template: `
    <div class="deduplication-summary">
      <p-table [value]="signals?.deduplicationRequest().materialsDeduplication | sortByPrimary" class="table m-table">
        <ng-template pTemplate="header" let-material>
          <tr>
            <th>
              {{'layout.relationship-popup.materials-code' | translate}}
            </th>
            <th>
              {{'layout.relationship-popup.material-description' | translate}}
            </th>
            <th>
              {{'layout.relationship-popup.md-statuses' | translate }}
            </th>
            <th>
              {{'deduplication.steps.summary.tableColumn.relationshipType' | translate}}
            </th>
            <th>
              {{'deduplication.steps.summary.tableColumn.subtype' | translate}}
            </th>
            <th></th>
            <th></th>
          </tr>
        </ng-template>
        <ng-template pTemplate="body" let-material>
          <tr>
            <td>
                #{{material.client}} / {{material.materialCode}}
            </td>
            <td>
                {{material.description}}
            </td>
            <td>
                {{material.status}} - {{material.statusDescription}}
            </td>
            <td>
                {{signals?.deduplicationRequest().relationshipType}}
            </td>
            <td>
                {{getSubtypeValue(signals?.deduplicationRequest().relationshipSubType)}}
            </td>
            <td>
                <span *ngIf="material.completeness" class="m-badge m-badge--danger m-badge--wide completeness completeness-{{ material.completeness | lowercase}}">
                  {{ material.completeness }}
                </span>
            </td>
            <td>
              <label class="fancy-checkbox">
                <input
                    type="checkbox"
                    [disabled]="true"
                    [checked]="material.primary"
                />
                <span></span>
              </label>
            </td>
          </tr>
        </ng-template>
      </p-table>
      @if (signals?.enrichedPlantData() && plantTableData()?.length > 0) {
        <span class="mt-3">{{'deduplication.steps.summary.plantChangesTitle' | translate}}</span>
        <p-table [value]="plantTableData()"
                 styleClass="p-datatable-compat mb-3"
                 [rowGroupMode]="'subheader'"
                 groupRowsBy="groupBy">
          <ng-template pTemplate="header">
            <tr>
              <th>
                {{'deduplication.steps.summary.tableColumn.plantCode' | translate}}
              </th>
              <th>
                {{'deduplication.steps.summary.tableColumn.plantDescription' | translate}}
              </th>
              <th>
                {{'deduplication.steps.summary.tableColumn.plantChangeType' | translate}}
              </th>
            </tr>
          </ng-template>
          <ng-template pTemplate="groupheader" let-rowData>
            <tr pRowGroupHeader class="p-rowgroup-header">
              <td [attr.colspan]="3">
                <span class="font-bold ml-2">{{ 'deduplication.enrichment.client' | translate }}: {{ rowData.client }}</span>
              </td>
            </tr>
          </ng-template>
          <ng-template pTemplate="body" let-plant>
            <tr>
              <td>
                {{plant.client}}/{{plant.plantCode}}
              </td>
              <td>
                {{plant.plantCode | plantTranslate: plant.client}}
              </td>
              <td>
                {{(plant.extension ? 'deduplication.enrichment.plant.extension' : 'deduplication.enrichment.plant.update') | translate}}
              </td>
            </tr>
          </ng-template>
        </p-table>
      }
    </div>
  `,
  styleUrls: ['md-selector/relationship-md-selector.component.scss'],
  standalone: true,
  imports: [
    CommonModule,
    FontAwesomeModule,
    TranslateModule,
    TableModule,
    ReactiveFormsModule,
    SortByPrimaryPipe,
    Tam4TranslationModule
]
})
export class DeduplicationSummaryComponent extends TamAbstractReduxComponent<SelectorMap>{
  @Input() mdDomain: string;

  constructor(protected translate: TranslateService,
              protected tamTranslate: Tam4TranslationService,
              protected store: Store
  ) {
      super(translate, tamTranslate, store, storeSelectors);
  }

  plantTableData = computed(() => {
    const enrichedPlantData = this.signals?.enrichedPlantData();
    if (!enrichedPlantData) {
      return [];
    }

    const tableData: any[] = [];

    Object.keys(enrichedPlantData).forEach(client => {
      const clientPlantData = enrichedPlantData[client];
      Object.keys(clientPlantData).forEach(plantKey => {
        const plantFields = clientPlantData[plantKey];
        if (plantFields.enrichmentStatus === 'EDITED' ) {
          tableData.push({
            client,
            plantCode: plantKey,
            plantDescription: '',
            changeType: plantFields.enrichmentStatus,
            extension: plantFields.extension ?? false,
            groupBy: client
          });
        }
      });
    });

    return tableData;
  });

  // getMdStatusDescription(materialClient: string, statusKey: string) {
  //   const statuses = this.signals?.availableStatusesByClient();
  //   const statusesByClient = statuses[materialClient];
  //   return statusesByClient.find(entry => entry.key === statusKey) ? statusesByClient.find(entry => entry.key === statusKey).value : statusKey;
  // }

  getSubtypeValue(subtype: Subtype) {
    return subtype?.value ?? '';
  }

}
